import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useQueryClient } from "@tanstack/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Database } from "@/integrations/supabase/types";
import { formatPrice } from "@/lib/price-utils";
import {
  deletePropertyImages,
  deleteCarImages,
  deleteHotelImages,
} from "@/lib/storage-utils";

// Define types for property and car listings
type PropertyListing = {
  id: string; // All tables use UUID strings
  title: string;
  description: string;
  location: string;
  price: number;
  beds: number;
  baths: number;
  images: string[];
  owner_id: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  owner_first_name?: string;
  owner_last_name?: string;
};

type CarListing = {
  id: string; // All tables use UUID strings
  title: string;
  description: string;
  make: string;
  model: string;
  year: number;
  seats: number;
  transmission: string;
  fuel_type: string;
  price_day: number;
  price_week: number;
  price_month: number;
  images: string[];
  features: string[];
  car_type:
    | "sedan"
    | "suv"
    | "luxury"
    | "compact"
    | "convertible"
    | "van"
    | "truck";
  owner_id: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  owner_first_name?: string;
  owner_last_name?: string;
};

type HotelListing = {
  id: string; // Hotels use UUID strings, not BIGINT numbers
  title: string;
  description: string;
  location: string;
  check_in_time: string;
  check_out_time: string;
  images: string[];
  amenities: string[];
  owner_id: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  owner_first_name?: string;
  owner_last_name?: string;
};

type Listing = PropertyListing | CarListing | HotelListing;

const AdminListings = () => {
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("all");
  const [properties, setProperties] = useState<PropertyListing[]>([]);
  const [hotels, setHotels] = useState<HotelListing[]>([]);
  const [cars, setCars] = useState<CarListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedListing, setSelectedListing] = useState<Listing | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    "all" | "pending" | "approved" | "rejected"
  >("all");

  useEffect(() => {
    fetchListings();
  }, [activeTab]);

  const fetchListings = async () => {
    setLoading(true);
    try {
      // Fetch properties
      let propertyQuery = supabase.from("properties").select("*");

      if (activeTab === "pending") {
        propertyQuery = propertyQuery.eq("status", "pending");
      } else if (activeTab === "approved") {
        propertyQuery = propertyQuery.eq("status", "approved");
      }

      // Order by creation date (latest first)
      propertyQuery = propertyQuery.order("created_at", { ascending: false });

      const { data: propertiesData, error: propertiesError } =
        await propertyQuery;

      if (propertiesError) {
        console.error("Properties fetch error:", propertiesError);
        throw propertiesError;
      }

      // First collect owner IDs from properties only
      const propertyOwnerIds =
        propertiesData?.map((property) => property.owner_id) || [];

      // Fetch hotels
      let hotelQuery = supabase.from("hotels").select("*");

      if (activeTab === "pending") {
        hotelQuery = hotelQuery.eq("status", "pending");
      } else if (activeTab === "approved") {
        hotelQuery = hotelQuery.eq("status", "approved");
      }

      // Order by creation date (latest first)
      hotelQuery = hotelQuery.order("created_at", { ascending: false });

      const { data: hotelsData, error: hotelsError } = await hotelQuery;

      if (hotelsError) {
        console.error("Hotels fetch error:", hotelsError);
        throw hotelsError;
      }

      // Fetch cars
      let carQuery = supabase.from("cars").select("*");

      if (activeTab === "pending") {
        carQuery = carQuery.eq("status", "pending");
      } else if (activeTab === "approved") {
        carQuery = carQuery.eq("status", "approved");
      }

      // Order by creation date (latest first)
      carQuery = carQuery.order("created_at", { ascending: false });

      const { data: carsData, error: carsError } = await carQuery;

      if (carsError) {
        console.error("Cars fetch error:", carsError);
        throw carsError;
      }

      // Now collect all owner IDs from properties, hotels, and cars
      const hotelOwnerIds = hotelsData?.map((hotel) => hotel.owner_id) || [];
      const carOwnerIds = carsData?.map((car) => car.owner_id) || [];

      // Combine and deduplicate owner IDs
      const allOwnerIds = [
        ...new Set([...propertyOwnerIds, ...hotelOwnerIds, ...carOwnerIds]),
      ];

      const { data: ownersData, error: ownersError } = await supabase
        .from("profiles")
        .select("id, first_name, last_name")
        .in("id", allOwnerIds);

      if (ownersError) throw ownersError;

      // Create a map of owner data
      const ownersMap: Record<
        string,
        { first_name?: string; last_name?: string }
      > = {};
      ownersData?.forEach((owner) => {
        ownersMap[owner.id] = {
          first_name: owner.first_name || "",
          last_name: owner.last_name || "",
        };
      });

      // Transform the data to add owner_first_name and owner_last_name properties
      const transformedProperties =
        propertiesData?.map((property) => ({
          ...property,
          owner_first_name: ownersMap[property.owner_id]?.first_name || "",
          owner_last_name: ownersMap[property.owner_id]?.last_name || "",
        })) || [];

      const transformedHotels =
        hotelsData?.map((hotel) => ({
          ...hotel,
          owner_first_name: ownersMap[hotel.owner_id]?.first_name || "",
          owner_last_name: ownersMap[hotel.owner_id]?.last_name || "",
        })) || [];

      const transformedCars =
        carsData?.map((car) => ({
          ...car,
          owner_first_name: ownersMap[car.owner_id]?.first_name || "",
          owner_last_name: ownersMap[car.owner_id]?.last_name || "",
        })) || [];

      setProperties(transformedProperties);
      setHotels(transformedHotels);
      setCars(transformedCars);
    } catch (error: any) {
      console.error("Error fetching listings:", error);
      toast.error("Failed to fetch listings: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  const viewListingDetails = async (listing: Listing) => {
    // If it's a hotel, fetch room types as well
    if (isHotelListing(listing)) {
      try {
        const { data: roomTypes, error } = await supabase
          .from("room_types")
          .select("*")
          .eq("hotel_id", listing.id)
          .order("created_at", { ascending: true });

        if (error) throw error;

        // Add room types to the hotel listing
        const hotelWithRoomTypes = {
          ...listing,
          room_types: roomTypes || [],
        };
        setSelectedListing(hotelWithRoomTypes);
      } catch (error) {
        console.error("Error fetching room types:", error);
        setSelectedListing(listing);
      }
    } else {
      setSelectedListing(listing);
    }

    setIsDetailDialogOpen(true);
  };

  // Check if a listing is a car listing
  const isCarListing = (listing: Listing): listing is CarListing => {
    return "make" in listing && "model" in listing;
  };

  // Check if a listing is a property listing
  const isPropertyListing = (listing: Listing): listing is PropertyListing => {
    return "beds" in listing && "baths" in listing;
  };

  // Check if a listing is a hotel listing
  const isHotelListing = (listing: Listing): listing is HotelListing => {
    return (
      "check_in_time" in listing &&
      "check_out_time" in listing &&
      "amenities" in listing
    );
  };

  const updateListingStatus = async (
    id: string,
    status: "pending" | "approved" | "rejected"
  ) => {
    try {
      // Basic validation
      if (!selectedListing) {
        throw new Error("No listing selected");
      }

      const updateData = {
        status,
        updated_at: new Date().toISOString(),
      };

      let tableName = "";
      let updateResult;

      // Determine table name and perform update
      if (isPropertyListing(selectedListing!)) {
        tableName = "properties";
      } else if (isHotelListing(selectedListing!)) {
        tableName = "hotels";
      } else {
        tableName = "cars";
      }

      // Get admin session token for authentication
      const adminSessionToken = localStorage.getItem("admin_session_token");
      if (!adminSessionToken) {
        throw new Error("Admin session not found. Please log in again.");
      }

      // Use admin RPC function to update listing status
      try {
        const { data, error } = await supabase.rpc(
          "admin_update_listing_status",
          {
            listing_table: tableName,
            listing_id: id,
            new_status: status,
            admin_session_token: adminSessionToken,
          }
        );

        if (error) {
          throw error;
        }

        if (!data || !data.success) {
          throw new Error(
            `Failed to update ${tableName}: ${data?.message || "Unknown error"}`
          );
        }

        // Set updateResult for compatibility with existing code
        updateResult = {
          data: [data.updated_listing],
          error: null,
        };
      } catch (dbError) {
        throw new Error(`Failed to update ${tableName}: ${dbError.message}`);
      }

      // Verify the update actually worked by checking the returned data
      if (updateResult.data && updateResult.data.length > 0) {
        const updatedRecord = updateResult.data[0];

        if (updatedRecord.status === status) {
          toast.success(`Listing status updated to ${status}`);
        } else {
          console.error(
            `❌ Update failed: Expected status '${status}' but got '${updatedRecord.status}'`
          );
          toast.error(`Update failed: Status not changed in database`);
          return;
        }
      } else {
        console.error(
          "❌ Update failed: No data returned from update operation"
        );
        toast.error("Update failed: No confirmation from database");
        return;
      }

      // Refetch all listings to ensure data consistency
      await fetchListings();

      // Invalidate public listing caches to reflect changes immediately
      queryClient.invalidateQueries({ queryKey: ["properties"] });
      queryClient.invalidateQueries({ queryKey: ["hotels"] });
      queryClient.invalidateQueries({ queryKey: ["cars"] });
      queryClient.invalidateQueries({ queryKey: ["featured-properties"] });

      // Additional verification: Check if the listing still has the correct status after refetch
      setTimeout(async () => {
        let verificationQuery;

        if (tableName === "properties") {
          verificationQuery = supabase
            .from("properties")
            .select("id, status")
            .eq("id", id)
            .single();
        } else if (tableName === "hotels") {
          verificationQuery = supabase
            .from("hotels")
            .select("id, status")
            .eq("id", id)
            .single();
        } else {
          verificationQuery = supabase
            .from("cars")
            .select("id, status")
            .eq("id", id)
            .single();
        }

        const { data: verificationData, error: verificationError } =
          await verificationQuery;

        if (verificationError) {
          console.error("Verification failed:", verificationError);
        } else {
          if (verificationData?.status !== status) {
            console.error(
              `❌ Verification failed: Status reverted to '${verificationData?.status}'`
            );
            toast.error(
              "Warning: Status may have reverted. Please refresh and check."
            );
          }
        }
      }, 2000);
    } catch (error: any) {
      console.error("Error updating listing status:", error);
      console.error("Error details:", error);
      toast.error("Failed to update listing status: " + error.message);
    }
  };

  const deleteListing = async (id: string) => {
    if (!selectedListing) return;

    try {
      // Get admin session token for authentication
      const adminSessionToken = localStorage.getItem("admin_session_token");
      if (!adminSessionToken) {
        throw new Error("Admin session not found. Please log in again.");
      }

      // Determine table name
      let tableName = "";
      if (isPropertyListing(selectedListing)) {
        tableName = "properties";
      } else if (isHotelListing(selectedListing)) {
        tableName = "hotels";
      } else {
        tableName = "cars";
      }

      // Use admin RPC function to delete listing with images
      const { data, error } = await supabase.rpc(
        "admin_delete_listing_with_images",
        {
          listing_table: tableName,
          listing_id: id,
          admin_session_token: adminSessionToken,
        }
      );

      if (error) {
        throw error;
      }

      if (!data || !data.success) {
        throw new Error(
          `Failed to delete ${tableName}: ${data?.message || "Unknown error"}`
        );
      }

      // Delete associated images from storage using admin function
      if (data.images_to_delete && data.images_to_delete.length > 0) {
        // Determine bucket name
        let bucketName = "";
        switch (tableName) {
          case "properties":
            bucketName = "property-images";
            break;
          case "cars":
            bucketName = "car-images";
            break;
          case "hotels":
            bucketName = "hotel-images";
            break;
        }

        // Extract file paths from URLs
        const filePaths = data.images_to_delete
          .map((url: string) => {
            // Extract filename from Supabase storage URL
            const match = url.match(new RegExp(`/${bucketName}/(.+)$`));
            return match ? match[1] : null;
          })
          .filter(Boolean);

        if (filePaths.length > 0) {
          try {
            // Use admin RPC function to delete storage files
            const { data: storageResult, error: storageError } =
              await supabase.rpc("admin_delete_storage_files", {
                bucket_name: bucketName,
                file_paths: filePaths,
                admin_session_token: adminSessionToken,
              });

            if (storageError) {
              console.error("Storage deletion error:", storageError);
            }
          } catch (storageErr) {
            console.error("Exception during storage deletion:", storageErr);
          }
        }
      }

      toast.success("Listing deleted successfully!");

      // Close dialog and refresh listings
      setIsDetailDialogOpen(false);
      setSelectedListing(null);
      fetchListings();
    } catch (error: any) {
      console.error("Error deleting listing:", error);
      toast.error("Failed to delete listing: " + error.message);
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Manage Listings</h1>

      <Tabs
        defaultValue="all"
        value={activeTab}
        onValueChange={setActiveTab}
        className="mb-6"
      >
        <TabsList className="mb-4">
          <TabsTrigger value="all">All Listings</TabsTrigger>
          <TabsTrigger value="pending">Pending Approval</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
        </TabsList>
      </Tabs>

      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
        </div>
      ) : (
        <>
          <h2 className="text-xl font-semibold mb-4">
            Properties ({properties.length})
          </h2>
          <div className="rounded-md border mb-8 overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead className="hidden sm:table-cell">
                      Location
                    </TableHead>
                    <TableHead className="hidden md:table-cell">
                      Owner
                    </TableHead>
                    <TableHead className="hidden lg:table-cell">
                      Price
                    </TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {properties.length > 0 ? (
                    properties.map((property) => (
                      <TableRow key={property.id}>
                        <TableCell className="font-medium">
                          <div>
                            <div className="font-medium">{property.title}</div>
                            {/* Show location on mobile */}
                            <div className="text-sm text-muted-foreground sm:hidden">
                              {property.location}
                            </div>
                            {/* Show owner on small screens */}
                            <div className="text-sm text-muted-foreground md:hidden">
                              {property.owner_first_name}{" "}
                              {property.owner_last_name}
                            </div>
                            {/* Show price on medium screens */}
                            <div className="text-sm text-muted-foreground lg:hidden">
                              ${property.price}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          {property.location}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {property.owner_first_name} {property.owner_last_name}
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          ${property.price}
                        </TableCell>
                        <TableCell>
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              property.status === "approved"
                                ? "bg-green-100 text-green-800"
                                : property.status === "pending"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {property.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => viewListingDetails(property)}
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        No properties found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          <h2 className="text-xl font-semibold mb-4">
            Hotels ({hotels.length})
          </h2>
          <div className="rounded-md border mb-8 overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead className="hidden sm:table-cell">
                      Location
                    </TableHead>
                    <TableHead className="hidden md:table-cell">
                      Owner
                    </TableHead>
                    <TableHead className="hidden lg:table-cell">
                      Check-in/out
                    </TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {hotels.length > 0 ? (
                    hotels.map((hotel) => (
                      <TableRow key={hotel.id}>
                        <TableCell className="font-medium">
                          <div>
                            <div className="font-medium">{hotel.title}</div>
                            {/* Show location on mobile */}
                            <div className="text-sm text-muted-foreground sm:hidden">
                              {hotel.location}
                            </div>
                            {/* Show owner on small screens */}
                            <div className="text-sm text-muted-foreground md:hidden">
                              {hotel.owner_first_name} {hotel.owner_last_name}
                            </div>
                            {/* Show check-in/out on medium screens */}
                            <div className="text-xs text-muted-foreground lg:hidden">
                              <div>In: {hotel.check_in_time}</div>
                              <div>Out: {hotel.check_out_time}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          {hotel.location}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {hotel.owner_first_name} {hotel.owner_last_name}
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <div className="text-xs">
                            <div>In: {hotel.check_in_time}</div>
                            <div>Out: {hotel.check_out_time}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              hotel.status === "approved"
                                ? "bg-green-100 text-green-800"
                                : hotel.status === "pending"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {hotel.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => viewListingDetails(hotel)}
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        No hotels found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          <h2 className="text-xl font-semibold mb-4">Cars ({cars.length})</h2>
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead className="hidden sm:table-cell">
                      Make/Model
                    </TableHead>
                    <TableHead className="hidden md:table-cell">
                      Owner
                    </TableHead>
                    <TableHead className="hidden lg:table-cell">
                      Price (Daily)
                    </TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {cars.length > 0 ? (
                    cars.map((car) => (
                      <TableRow key={car.id}>
                        <TableCell className="font-medium">
                          <div>
                            <div className="font-medium">{car.title}</div>
                            {/* Show make/model on mobile */}
                            <div className="text-sm text-muted-foreground sm:hidden">
                              {car.make} {car.model}
                            </div>
                            {/* Show owner on small screens */}
                            <div className="text-sm text-muted-foreground md:hidden">
                              {car.owner_first_name} {car.owner_last_name}
                            </div>
                            {/* Show price on medium screens */}
                            <div className="text-sm text-muted-foreground lg:hidden">
                              ${car.price_day}/day
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          {car.make} {car.model}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {car.owner_first_name} {car.owner_last_name}
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          ${car.price_day}
                        </TableCell>
                        <TableCell>
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              car.status === "approved"
                                ? "bg-green-100 text-green-800"
                                : car.status === "pending"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {car.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => viewListingDetails(car)}
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        No cars found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </>
      )}

      {/* Enhanced Listing Details Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-5xl max-h-[95vh] overflow-hidden p-0">
          {selectedListing ? (
            <div className="flex flex-col h-full">
              {/* Header */}
              <DialogHeader className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
                <DialogTitle className="text-xl font-bold text-gray-800">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-2xl">{selectedListing.title}</span>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-sm text-gray-600">
                          {isCarListing(selectedListing)
                            ? "🚗 Car Rental"
                            : isHotelListing(selectedListing)
                            ? "🏨 Hotel"
                            : "🏠 Property"}
                        </span>
                        <span
                          className={`px-3 py-1 rounded-full text-xs font-medium ${
                            selectedListing.status === "approved"
                              ? "bg-green-100 text-green-800"
                              : selectedListing.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {selectedListing.status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                  </div>
                </DialogTitle>
              </DialogHeader>

              {/* Scrollable Content */}
              <div className="flex-1 overflow-y-auto px-6 py-4">
                <div className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Description */}
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-lg font-semibold text-gray-800 mb-2">
                          📝 Description
                        </h4>
                        <p className="text-gray-600 leading-relaxed bg-gray-50 p-4 rounded-lg">
                          {selectedListing.description}
                        </p>
                      </div>

                      {/* Location */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-800 mb-2">
                          📍 Location
                        </h4>
                        <p className="text-gray-700 bg-blue-50 p-3 rounded-lg">
                          {selectedListing.location}
                        </p>
                      </div>
                    </div>

                    {/* Owner & Listing Info */}
                    <div className="space-y-4">
                      <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-lg">
                        <h4 className="text-lg font-semibold text-gray-800 mb-3">
                          📋 Listing Information
                        </h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Owner:</span>
                            <span className="font-medium">
                              {selectedListing.owner_first_name}{" "}
                              {selectedListing.owner_last_name}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Created:</span>
                            <span className="font-medium">
                              {new Date(
                                selectedListing.created_at
                              ).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Last Updated:</span>
                            <span className="font-medium">
                              {new Date(
                                selectedListing.updated_at
                              ).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">ID:</span>
                            <span className="font-mono text-xs bg-gray-200 px-2 py-1 rounded">
                              {selectedListing.id}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Price Information */}
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">
                      💰 Pricing
                    </h4>
                    {isPropertyListing(selectedListing) ? (
                      <div className="text-center">
                        <p className="text-3xl font-bold text-green-600">
                          ${formatPrice(selectedListing.price)}
                        </p>
                        <p className="text-gray-600">per night</p>
                      </div>
                    ) : isHotelListing(selectedListing) ? (
                      <div className="text-center">
                        <p className="text-lg font-semibold text-blue-600">
                          Room-based pricing
                        </p>
                        <p className="text-sm text-gray-600">
                          See room types below
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Daily</p>
                          <p className="text-xl font-bold text-green-600">
                            ${formatPrice(selectedListing.price_day)}
                          </p>
                        </div>
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Weekly</p>
                          <p className="text-xl font-bold text-green-600">
                            ${formatPrice(selectedListing.price_week)}
                          </p>
                        </div>
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Monthly</p>
                          <p className="text-xl font-bold text-green-600">
                            ${formatPrice(selectedListing.price_month)}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Property Specific Details */}
                  {isPropertyListing(selectedListing) && (
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200">
                      <h4 className="text-lg font-semibold text-gray-800 mb-3">
                        🏠 Property Details
                      </h4>
                      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Bedrooms</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {selectedListing.beds}
                          </p>
                        </div>
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Bathrooms</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {selectedListing.baths}
                          </p>
                        </div>
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Property Type</p>
                          <p className="text-sm font-bold text-blue-600">
                            {(selectedListing as any).property_type}
                          </p>
                        </div>
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Features</p>
                          <p className="text-sm font-bold text-blue-600">
                            {(selectedListing as any).features?.length || 0}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Hotel Specific Details */}
                  {isHotelListing(selectedListing) && (
                    <div className="space-y-4">
                      <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200">
                        <h4 className="text-lg font-semibold text-gray-800 mb-3">
                          🏨 Hotel Details
                        </h4>
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                          <div className="text-center bg-white p-3 rounded-lg">
                            <p className="text-gray-600 text-sm">Amenities</p>
                            <p className="text-2xl font-bold text-purple-600">
                              {selectedListing.amenities?.length || 0}
                            </p>
                          </div>
                          <div className="text-center bg-white p-3 rounded-lg">
                            <p className="text-gray-600 text-sm">Check-in</p>
                            <p className="text-lg font-bold text-purple-600">
                              {selectedListing.check_in_time}
                            </p>
                          </div>
                          <div className="text-center bg-white p-3 rounded-lg">
                            <p className="text-gray-600 text-sm">Check-out</p>
                            <p className="text-lg font-bold text-purple-600">
                              {selectedListing.check_out_time}
                            </p>
                          </div>
                          <div className="text-center bg-white p-3 rounded-lg">
                            <p className="text-gray-600 text-sm">Room Types</p>
                            <p className="text-2xl font-bold text-purple-600">
                              {(selectedListing as any).room_types?.length || 0}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Room Types Details */}
                      {(selectedListing as any).room_types &&
                        (selectedListing as any).room_types.length > 0 && (
                          <div className="bg-gradient-to-br from-orange-50 to-yellow-50 p-4 rounded-lg border border-orange-200">
                            <h4 className="text-lg font-semibold text-gray-800 mb-3">
                              🛏️ Room Types
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {(selectedListing as any).room_types.map(
                                (roomType: any, index: number) => (
                                  <div
                                    key={index}
                                    className="bg-white p-4 rounded-lg border border-orange-100"
                                  >
                                    <h5 className="font-semibold text-lg text-gray-800 mb-2">
                                      {roomType.name}
                                    </h5>
                                    <div className="space-y-2 text-sm">
                                      <div className="flex justify-between">
                                        <span className="text-gray-600">
                                          Price:
                                        </span>
                                        <span className="font-bold text-green-600">
                                          ${roomType.base_price}/night
                                        </span>
                                      </div>
                                      <div className="flex justify-between">
                                        <span className="text-gray-600">
                                          Max Occupancy:
                                        </span>
                                        <span className="font-medium">
                                          {roomType.max_occupancy} guests
                                        </span>
                                      </div>
                                      {roomType.bed_configuration && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-600">
                                            Bed Config:
                                          </span>
                                          <span className="font-medium">
                                            {roomType.bed_configuration}
                                          </span>
                                        </div>
                                      )}
                                      {roomType.room_size_sqm && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-600">
                                            Size:
                                          </span>
                                          <span className="font-medium">
                                            {roomType.room_size_sqm} sqm
                                          </span>
                                        </div>
                                      )}
                                      {roomType.description && (
                                        <div className="mt-2">
                                          <p className="text-gray-600 text-xs">
                                            {roomType.description}
                                          </p>
                                        </div>
                                      )}
                                      {roomType.amenities &&
                                        roomType.amenities.length > 0 && (
                                          <div className="mt-2">
                                            <p className="text-gray-600 text-xs mb-1">
                                              Amenities:
                                            </p>
                                            <div className="flex flex-wrap gap-1">
                                              {roomType.amenities
                                                .slice(0, 3)
                                                .map(
                                                  (
                                                    amenity: string,
                                                    idx: number
                                                  ) => (
                                                    <span
                                                      key={idx}
                                                      className="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs"
                                                    >
                                                      {amenity}
                                                    </span>
                                                  )
                                                )}
                                              {roomType.amenities.length >
                                                3 && (
                                                <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                                                  +
                                                  {roomType.amenities.length -
                                                    3}{" "}
                                                  more
                                                </span>
                                              )}
                                            </div>
                                          </div>
                                        )}
                                    </div>
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )}
                    </div>
                  )}

                  {/* Car Specific Details */}
                  {isCarListing(selectedListing) && (
                    <div className="bg-gradient-to-br from-red-50 to-orange-50 p-4 rounded-lg border border-red-200">
                      <h4 className="text-lg font-semibold text-gray-800 mb-3">
                        🚗 Vehicle Details
                      </h4>
                      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Make</p>
                          <p className="text-lg font-bold text-red-600">
                            {selectedListing.make}
                          </p>
                        </div>
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Model</p>
                          <p className="text-lg font-bold text-red-600">
                            {selectedListing.model}
                          </p>
                        </div>
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Year</p>
                          <p className="text-lg font-bold text-red-600">
                            {selectedListing.year}
                          </p>
                        </div>
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Seats</p>
                          <p className="text-lg font-bold text-red-600">
                            {selectedListing.seats}
                          </p>
                        </div>
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Transmission</p>
                          <p className="text-sm font-bold text-red-600">
                            {selectedListing.transmission}
                          </p>
                        </div>
                        <div className="text-center bg-white p-3 rounded-lg">
                          <p className="text-gray-600 text-sm">Fuel Type</p>
                          <p className="text-sm font-bold text-red-600">
                            {selectedListing.fuel_type}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Features/Amenities */}
                  {isCarListing(selectedListing) &&
                    selectedListing.features &&
                    selectedListing.features.length > 0 && (
                      <div className="bg-gradient-to-br from-teal-50 to-cyan-50 p-4 rounded-lg border border-teal-200">
                        <h4 className="text-lg font-semibold text-gray-800 mb-3">
                          ✨ Car Features
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedListing.features.map((feature, index) => (
                            <span
                              key={index}
                              className="px-3 py-2 bg-teal-100 text-teal-800 rounded-full text-sm font-medium"
                            >
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                  {/* Hotel Amenities */}
                  {isHotelListing(selectedListing) &&
                    selectedListing.amenities &&
                    selectedListing.amenities.length > 0 && (
                      <div className="bg-gradient-to-br from-indigo-50 to-blue-50 p-4 rounded-lg border border-indigo-200">
                        <h4 className="text-lg font-semibold text-gray-800 mb-3">
                          🏨 Hotel Amenities
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedListing.amenities.map((amenity, index) => (
                            <span
                              key={index}
                              className="px-3 py-2 bg-indigo-100 text-indigo-800 rounded-full text-sm font-medium"
                            >
                              {amenity}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                  {/* Property Features */}
                  {isPropertyListing(selectedListing) &&
                    (selectedListing as any).features &&
                    (selectedListing as any).features.length > 0 && (
                      <div className="bg-gradient-to-br from-emerald-50 to-green-50 p-4 rounded-lg border border-emerald-200">
                        <h4 className="text-lg font-semibold text-gray-800 mb-3">
                          🏠 Property Features
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {(selectedListing as any).features.map(
                            (feature: string, index: number) => (
                              <span
                                key={index}
                                className="px-3 py-2 bg-emerald-100 text-emerald-800 rounded-full text-sm font-medium"
                              >
                                {feature}
                              </span>
                            )
                          )}
                        </div>
                      </div>
                    )}

                  {/* Images Gallery */}
                  <div className="bg-gradient-to-br from-slate-50 to-gray-50 p-4 rounded-lg border border-slate-200">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">
                      📸 Images Gallery
                    </h4>
                    {selectedListing.images &&
                    selectedListing.images.length > 0 ? (
                      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
                        {selectedListing.images.map((image, index) => (
                          <div
                            key={index}
                            className="aspect-square bg-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                          >
                            <img
                              src={image}
                              alt={`${selectedListing.title} image ${
                                index + 1
                              }`}
                              className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                            />
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-gray-500 italic">
                          📷 No images available
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced Footer with Action Buttons */}
          <div className="border-t bg-gray-50 px-6 py-4">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <div className="flex flex-wrap gap-2">
                {selectedListing.status === "pending" && (
                  <>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        updateListingStatus(selectedListing.id, "rejected");
                        setIsDetailDialogOpen(false);
                      }}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      ❌ Reject
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => {
                        updateListingStatus(selectedListing.id, "approved");
                        setIsDetailDialogOpen(false);
                      }}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      ✅ Approve
                    </Button>
                  </>
                )}
                {selectedListing.status === "approved" && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      updateListingStatus(selectedListing.id, "rejected");
                      setIsDetailDialogOpen(false);
                    }}
                    className="bg-orange-600 hover:bg-orange-700"
                  >
                    ⏸️ Suspend
                  </Button>
                )}
                {selectedListing.status === "rejected" && (
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => {
                      updateListingStatus(selectedListing.id, "approved");
                      setIsDetailDialogOpen(false);
                    }}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    🔄 Reinstate
                  </Button>
                )}
              </div>

              <div className="flex gap-2">
                {/* Delete Button */}
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="bg-red-600 hover:bg-red-700"
                    >
                      🗑️ Delete
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Listing</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to permanently delete this
                        listing? This action cannot be undone. All associated
                        data and images will be permanently removed.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => deleteListing(selectedListing.id)}
                        className="bg-red-600 hover:bg-red-700 text-white"
                      >
                        Delete Permanently
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsDetailDialogOpen(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
          ) : (
            <div className="p-6 text-center">
              <p className="text-gray-500">No listing selected</p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminListings;
